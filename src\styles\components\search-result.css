/* Page Layout */
.page-container {
  display: flex;
  flex-direction: column;
}

.main-layout {
  flex: 1;
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.content-wrapper {
  margin-top: 1rem;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  text-transform: capitalize;
  color: #313131;
  margin: 0 0 1rem 0;
}

/* Items Grid Layout */
.items-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr); /* Default: 2 columns on mobile */
}

@media (min-width: 640px) {
  .items-grid {
    grid-template-columns: repeat(3, 1fr); /* sm: 3 columns */
    gap: 1.25rem;
  }
}

@media (min-width: 768px) {
  .items-grid {
    grid-template-columns: repeat(4, 1fr); /* md: 4 columns */
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .items-grid {
    grid-template-columns: repeat(5, 1fr); /* lg: 5 columns */
  }
}

@media (min-width: 1280px) {
  .items-grid {
    grid-template-columns: repeat(6, 1fr); /* xl: 6 columns */
  }
}

/* Item Container */
.items-container {
  display: flex;
  flex-direction: column;
  text-decoration: none;
  cursor: pointer;
  border-radius: 8px;
  gap: 2px;
  overflow: hidden;
  background: white;
}

/* Image Wrapper */
.items-image-wrapper {
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  height: 160px; /* Default height for mobile */
  position: relative;
  background-color: #f3f3f3;
}

.items-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

@media (min-width: 640px) {
  .items-image-wrapper {
    height: 180px; /* sm and above: 180px */
  }
}

@media (min-width: 1024px) {
  .items-image-wrapper {
    height: 200px; /* lg and above: 200px */
  }
}

/* Text Content */
.items-text-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 4px 4px 4px;
}

.items-title {
  font-family: 'Istok Web', sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
  color: #313131;
  margin: 0;
  line-height: 1.3;
}

@media (min-width: 640px) {
  .items-title {
    font-size: 15px;
  }
}

@media (min-width: 1024px) {
  .items-title {
    font-size: 16px;
  }
}

.items-price {
  font-size: 14px;
  font-weight: 600;
  color: #4e5052;
  margin: 0 0 8px 0;
}

@media (min-width: 640px) {
  .items-price {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  .items-price {
    font-size: 14px;
  }
}

/* Rating and Sales Info */
.items-sold {
  display: flex;
  gap: 4px;
  font-size: 11px;
  color: #666;
  align-items: center;
  margin-top: 2px;
}

@media (min-width: 640px) {
  .items-sold {
    font-size: 12px;
  }
}

.items-sold svg {
  flex-shrink: 0;
}

.items-sold .rating {
  color: #FFD700;
  font-weight: 500;
}

.items-sold .separator {
  color: #ccc;
}

.items-sold .sold-count {
  color: #888;
}

/* Not Found Styles */
.not-found-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  gap: 16px;
  margin-top: 60px;
  padding: 2rem;
}

.not-found-image {
  height: 200px;
  width: auto;
  max-width: 100%;
  object-fit: contain;
}

@media (min-width: 640px) {
  .not-found-image {
    height: 250px;
  }
}

@media (min-width: 1024px) {
  .not-found-image {
    height: 300px;
  }
}

.not-found-text {
  color: #777;
  white-space: nowrap;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

/* Brands Styles (if needed) */
.brands-title {
  font-size: 12px;
  font-weight: 400;
  color: #313131;
}

.brands-image-container {
  overflow: hidden;
  border-radius: 8px;
  width: 100%;
  height: 110px;
}

.brands-image-skeleton {
  width: 100%;
  height: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f3f3;
  border-radius: 8px;
  overflow: hidden;
}

.brands-item {
  margin-top: 12px;
  width: 100%;
  height: 100%;
  display: flex;
  gap: 12px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.brands-container {
  display: grid;
  gap: 1.5rem;
  justify-content: space-between;
  grid-template-columns: repeat(3, 1fr);
}

@media (min-width: 640px) {
  .brands-container {
    grid-template-columns: repeat(4, 1fr);
  }
  .brands-image-container {
    height: 150px;
  }
}

  @media (min-width: 1280px) {
    .brands-container {
      grid-template-columns: repeat(5, 1fr);
    }
    .brands-image-container {
      height: 250px;
    }
    .brands-image-skeleton {
      height: 225px;
    }
  }

/* Loading States */
.items-container.loading {
  pointer-events: none;
  opacity: 0.7;
}

.items-image.loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Accessibility */
.items-container:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.items-container:focus:not(:focus-visible) {
  outline: none;
}