footer {
  background-color: #000;
  color: #444;
  border-top: 1px solid #323232;
  padding-top: 2.5rem;
  display: flex;
  flex-direction: column;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2.5rem;
  padding: 0 1rem 2.5rem;
  width: 100%;
  margin: 0 auto;
}

@media (min-width: 640px) {
  .footer-content {
    max-width: 700px;
  }
}

@media (min-width: 1024px) {
  .footer-content {
    grid-template-columns: repeat(3, 1fr);
    max-width: 1140px;
  }

  .footer-links {
    display: flex;
    gap: 2.5rem;
    justify-content: space-between;
  }
}

.footer-logo {
  display: inline-block;
  margin-bottom: 1rem;
}
.logo-image {
  width: 40px;
  height: 40px;
}

.footer-description {
  font-size: 0.875rem;
  line-height: 1.5;
  color: #d8d8d8;
}
.text-white{
   color: #d8d8d8;
}

.footer-section h2 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #fff;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.footer-section ul li a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}
.footer-section ul li a:hover {
  color: #eda412;
}

.contact-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-section i {
  color: #eda412;
}

.footer-section img.icon {
  width: 20px;
  height: 20px;
}

.map-info {
  margin-top: 1.5rem;
}

.map-row {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

.map-row img {
  width: 40px;
  height: 40px;
}

.map-row p {
  font-size: 0.875rem;
  color: #999;
}

.footer-bottom {
  border-top: 1px solid #323232;
  text-align: center;
  padding: 1.5rem 1rem;
  font-size: 0.875rem;
  color: #999;
}
